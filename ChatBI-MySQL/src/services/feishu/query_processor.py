"""
飞书查询处理器模块
负责协调整个查询处理流程
"""

import asyncio
import uuid
from src.utils.logger import logger
from src.services.feishu.message_apis import (
    reply_simple_text_message,
    send_updates_to_card,
    send_finished_message_to_card,
    get_message_content,
    MessageContent,
)
from .conversation_service import ConversationService
from .agent_service import AgentService
from .stream_processor import StreamProcessor
from .card_service import CardService
from .background_execution_manager import background_execution_manager


class QueryProcessor:
    """查询处理器"""

    @staticmethod
    async def handle_agent_query(
        message_id: str,
        user_query: str,
        user_info_dict: dict,
        root_id: str = None,
        parent_id: str = None,
        image_url: str = None,
    ):
        """处理用户查询并将更新流式传输到飞书卡片

        Args:
            message_id: 消息ID
            user_query: 用户查询
            user_info_dict: 用户信息字典
            root_id: 根消息ID
            parent_id: 父消息ID
            image_url: 图片URL, 既可以是URL，也可以是base64编码的内容
        """
        sequence = 0
        card_id = None
        element_id = None

        try:
            user_name = user_info_dict.get("name", "unknown_user")
            user_id = user_info_dict.get("open_id", "unknown_user")
            user_email = user_info_dict.get("email", "unknown")
            user_id_hash_code = user_id[-10:]  # 获取用户ID的最后10个字符

            logger.info(f"开始处理用户 {user_name} 的查询: {user_query}")

            # 使用root_id作为conversation_id，如果没有则使用message_id
            conversation_id = root_id if root_id else message_id
            logger.info(f"是否使用了root_id作为conversation_id: {conversation_id == root_id}")

            # 1. 验证对话所有权
            is_owner, is_exists = (
                await ConversationService.validate_conversation_ownership(
                    user_name, user_email, message_id, conversation_id
                )
            )
            if not is_owner and is_exists:
                logger.warning(f"对话已经存在，且不属于当前用户:{conversation_id}, {user_name} 跳过处理")
                return

            parent_conent = None
            if not is_exists and not is_owner:
                new_conversation_id = (
                    f"{root_id}_{user_id_hash_code}" if root_id else f"{message_id}_{user_id_hash_code}"
                )
                logger.warning(f"对话ID:{conversation_id}不属于当前用户:{user_name}, 将为他创建新的会话:{new_conversation_id}")
                conversation_id = new_conversation_id
                if parent_id:
                    logger.info(f"尝试获取父消息 {parent_id} 的内容")
                    parent_conent: MessageContent = get_message_content(parent_id)
                    if parent_conent and parent_conent.text:
                        logger.info(
                            f"使用父消息 {parent_id} 的内容作为上下文: {parent_conent.text}"
                        )
                        user_query = f"{parent_conent.text}, {user_query}"

            # 2. 尽早创建初始卡片，让用户快速收到响应
            card_id, element_id = await CardService.create_initial_card_early(
                message_id, user_query, conversation_id, user_id
            )
            if not card_id:
                logger.warning("卡片创建失败，但继续处理查询并尝试发送文本回复")
                # 不要直接返回，继续处理查询，最后发送文本回复

            # 3. 保存用户消息到历史记录
            await ConversationService.save_user_message_to_history(
                user_name, user_email, conversation_id, user_query
            )

            # 4. 获取对话历史记录
            history = await ConversationService.get_conversation_history(
                user_name, user_email, conversation_id, root_id
            )

            # 5. 初始化Agent
            bot, user_info_obj = await AgentService.initialize_agent_and_user_info(
                user_info_dict
            )

            # 6. 流式处理Agent响应
            if card_id:
                # 有卡片时使用流式处理
                (
                    last_sequence,
                    full_response,
                    full_log_message,
                    structured_assistant_message,
                    timeouted,
                    used_agents,
                ) = await StreamProcessor.process_agent_stream(
                    bot,
                    user_query,
                    user_info_obj,
                    card_id,
                    initial_sequence=1,
                    history=history,
                    image_url=image_url if image_url else parent_conent.image_url if parent_conent else None,
                )
            else:
                # 没有卡片时直接运行Agent并获取结果，并告知用户出错了
                reply_simple_text_message(message_id, f"卡片创建失败，无法处理您的查询:\n{user_query}\n请稍后再试")
                return

            # 7. 处理超时情况
            if timeouted and card_id:
                # 启动后台执行，不立即保存结果
                task_id = await QueryProcessor._handle_timeout_with_background_execution(
                    message_id,
                    user_query,
                    conversation_id,
                    user_id,
                    user_name,
                    user_email,
                    card_id,
                    full_response,
                    full_log_message,
                    structured_assistant_message,
                    used_agents,
                    bot,
                    element_id,
                )
                logger.info(f"超时处理完成，后台任务ID: {task_id}")
                return  # 超时情况下直接返回，不执行后续步骤
            else:
                sequence = last_sequence

            # 8. 完成卡片（如果有卡片的话）
            if card_id:
                await CardService.finish_card(card_id, sequence, conversation_id)

            # 9. 保存助手回复到历史记录（仅在非超时情况下）
            await ConversationService.save_assistant_response_to_history(
                user_name,
                user_email,
                conversation_id,
                full_response,
                full_log_message,
                structured_assistant_message,
                used_agents,
                bot  # 传递bot实例以获取Tool Agent日志
            )

            # 10. 如果没有卡片，发送文本回复
            if not card_id and full_response:
                logger.info(f"发送文本回复，长度: {len(full_response)}")
                reply_simple_text_message(message_id, full_response)

            logger.info(f"查询处理完成，最终回复长度: {len(full_response)}")

        except Exception as e:
            await QueryProcessor._handle_error(
                e, message_id, card_id, element_id, sequence
            )

    @staticmethod
    async def _handle_timeout(
        message_id: str,
        user_query: str,
        conversation_id: str,
        user_id: str,
        old_card_id: str,
        full_response: str,
        element_id: str,
    ) -> tuple[str, str, int]:
        """处理超时情况 - 新的实现：等待Agent完成后创建新卡片

        Returns:
            tuple: (card_id, element_id, sequence)
        """
        logger.warning("检测到流式响应超时，Agent将在后台继续执行")

        # 生成后台任务ID
        task_id = str(uuid.uuid4())

        # 定义完成回调函数
        async def completion_callback(task):
            """Agent执行完成后的回调函数"""
            try:
                if task.error:
                    logger.error(f"后台Agent执行出错: {task.error}")
                    return

                result_data = task.result_data
                if not result_data:
                    logger.warning("后台Agent执行完成但没有结果数据")
                    return

                # 创建新卡片展示完整结果
                await QueryProcessor._create_completion_card(
                    task.message_id,
                    task.user_query,
                    task.conversation_id,
                    task.user_id,
                    result_data
                )

                logger.info(f"后台任务 {task.task_id} 完成，已创建新卡片展示结果")

            except Exception as e:
                logger.error(f"执行完成回调时出错: {e}")

        # 注册后台任务
        background_execution_manager.register_background_task(
            task_id=task_id,
            message_id=message_id,
            user_query=user_query,
            conversation_id=conversation_id,
            user_id=user_id,
            completion_callback=completion_callback
        )

        logger.info(f"已注册后台执行任务 {task_id}，Agent将继续在后台执行")

        # 返回原卡片信息，不创建新卡片
        return old_card_id, element_id, 0

    @staticmethod
    async def _handle_timeout_with_background_execution(
        message_id: str,
        user_query: str,
        conversation_id: str,
        user_id: str,
        user_name: str,
        user_email: str,
        card_id: str,
        full_response: str,
        full_log_message: str,
        structured_assistant_message: dict,
        used_agents: list,
        bot_instance,
        element_id: str,
    ) -> str:
        """处理超时情况并启动后台执行

        Returns:
            str: 后台任务ID
        """
        logger.warning("检测到流式响应超时，启动后台执行模式")

        # 生成后台任务ID
        task_id = str(uuid.uuid4())

        # 准备结果数据
        result_data = {
            'full_response': full_response,
            'full_log_message': full_log_message,
            'structured_assistant_message': structured_assistant_message,
            'used_agents': used_agents,
            'user_name': user_name,
            'user_email': user_email,
            'bot_instance': bot_instance
        }

        # 定义完成回调函数
        async def completion_callback(task):
            """Agent执行完成后的回调函数"""
            try:
                if task.error:
                    logger.error(f"后台Agent执行出错: {task.error}")
                    return

                # 创建新卡片展示完整结果
                await QueryProcessor._create_completion_card(
                    task.message_id,
                    task.user_query,
                    task.conversation_id,
                    task.user_id,
                    task.result_data
                )

                logger.info(f"后台任务 {task.task_id} 完成，已创建新卡片展示结果")

            except Exception as e:
                logger.error(f"执行完成回调时出错: {e}")

        # 注册后台任务
        background_execution_manager.register_background_task(
            task_id=task_id,
            message_id=message_id,
            user_query=user_query,
            conversation_id=conversation_id,
            user_id=user_id,
            completion_callback=completion_callback
        )

        # 立即标记任务完成，因为StreamProcessor已经收集了所有可用的结果
        # 超时只是停止了流式更新，但Agent的执行结果已经在result_data中
        background_execution_manager.update_task_result(
            task_id=task_id,
            result_data=result_data,
            is_completed=True
        )

        logger.info(f"已启动后台执行任务 {task_id}，Agent结果将在新卡片中展示")
        return task_id

    @staticmethod
    async def _create_completion_card(
        message_id: str,
        user_query: str,
        conversation_id: str,
        user_id: str,
        result_data: dict
    ):
        """创建完成卡片展示Agent执行的完整结果

        Args:
            message_id: 消息ID
            user_query: 用户查询
            conversation_id: 对话ID
            user_id: 用户ID
            result_data: Agent执行结果数据
        """
        try:
            # 获取完整的执行结果
            full_response = result_data.get('full_response', '')
            full_log_message = result_data.get('full_log_message', '')
            structured_assistant_message = result_data.get('structured_assistant_message', {})
            used_agents = result_data.get('used_agents', [])

            # 尝试创建完成卡片
            new_card_id, new_element_id = await CardService.create_completion_card(
                message_id, user_query, conversation_id, user_id
            )

            if new_card_id:
                # 卡片创建成功，发送完整响应到新卡片
                logger.info(f"卡片创建成功，发送完整结果到卡片 {new_card_id}")
                sequence = 2  # 重置序列号
                sequence = await asyncio.to_thread(
                    send_updates_to_card,
                    card_id=new_card_id,
                    markdown_content=full_response,
                    element_id=new_element_id,
                    sequence=sequence,
                )

                # 完成卡片
                await CardService.finish_card(new_card_id, sequence, conversation_id)
            else:
                # 卡片创建失败，直接发送文本消息包含完整结果
                logger.warning("卡片创建失败，直接发送文本消息包含完整结果")

                # 构建包含完整结果的文本消息
                completion_message = f"""✅ Agent执行完成

您的查询：{user_query}

🤖 执行结果：

{full_response}

---
💡 如需查看更多详情，请访问网页版查看完整对话记录。"""

                # 发送文本消息
                await asyncio.to_thread(
                    reply_simple_text_message,
                    message_id,
                    completion_message
                )

            # 保存完整结果到数据库
            user_name = result_data.get('user_name', '')
            user_email = result_data.get('user_email', '')
            bot_instance = result_data.get('bot_instance')

            if user_name and user_email:
                await ConversationService.save_assistant_response_to_history(
                    user_name,
                    user_email,
                    conversation_id,
                    full_response,
                    full_log_message,
                    structured_assistant_message,
                    used_agents,
                    bot_instance
                )
                logger.info(f"后台执行结果已保存到数据库 (对话: {conversation_id})")

            logger.info(f"完成卡片创建成功，展示了完整的Agent执行结果")

        except Exception as e:
            logger.error(f"创建完成卡片时出错: {e}")

    @staticmethod
    async def _handle_error(
        error, message_id, card_id=None, element_id=None, sequence=0
    ):
        """处理查询过程中的错误

        Args:
            error: 错误对象
            message_id: 消息ID
            card_id: 卡片ID
            element_id: 元素ID
            sequence: 序列号
        """
        logger.error(f"处理Agent查询时出错: {error}", exc_info=True)

        # 尝试发送错误消息到卡片，如果可能的话，否则回复文本
        try:
            error_message = f"处理您的请求时发生内部错误。\n错误信息: {error}"
            if card_id:
                error_sequence = sequence + 1
                await asyncio.to_thread(
                    send_updates_to_card,
                    card_id,
                    error_message,
                    element_id,
                    sequence=error_sequence,
                )
                finish_sequence = error_sequence + 1
                await asyncio.to_thread(
                    send_finished_message_to_card,
                    card_id,
                    sequence=finish_sequence,
                    chat_id="invalid_chat_id",
                )
            else:
                reply_simple_text_message(message_id, error_message)
        except Exception as send_error:
            logger.error(f"发送错误消息到飞书时再次出错: {send_error}")
            reply_simple_text_message(
                message_id, "处理您的请求时发生内部错误，并且无法更新卡片状态。"
            )
