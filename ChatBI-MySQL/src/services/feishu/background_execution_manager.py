"""
飞书后台执行管理器模块
负责管理超时后的Agent后台执行状态跟踪和完成通知
"""
import asyncio
import time
import threading
from typing import Dict, Optional, Callable, Any
from dataclasses import dataclass
from src.utils.logger import logger


@dataclass
class BackgroundTask:
    """后台执行任务"""
    task_id: str
    message_id: str
    user_query: str
    conversation_id: str
    user_id: str
    start_time: float
    completion_callback: Optional[Callable] = None
    result_data: Optional[Dict[str, Any]] = None
    is_completed: bool = False
    error: Optional[str] = None


class BackgroundExecutionManager:
    """后台执行管理器"""
    
    def __init__(self):
        self.active_tasks: Dict[str, BackgroundTask] = {}
        self.lock = threading.Lock()
        self._cleanup_interval = 3600  # 1小时清理一次
        self._max_task_age = 7200  # 2小时后清理任务
        
        # 启动清理任务
        self._start_cleanup_task()
    
    def register_background_task(
        self,
        task_id: str,
        message_id: str,
        user_query: str,
        conversation_id: str,
        user_id: str,
        completion_callback: Optional[Callable] = None
    ) -> BackgroundTask:
        """注册后台执行任务
        
        Args:
            task_id: 任务ID
            message_id: 消息ID
            user_query: 用户查询
            conversation_id: 对话ID
            user_id: 用户ID
            completion_callback: 完成回调函数
            
        Returns:
            BackgroundTask: 注册的后台任务
        """
        task = BackgroundTask(
            task_id=task_id,
            message_id=message_id,
            user_query=user_query,
            conversation_id=conversation_id,
            user_id=user_id,
            start_time=time.time(),
            completion_callback=completion_callback
        )
        
        with self.lock:
            self.active_tasks[task_id] = task
            
        logger.info(f"注册后台执行任务: {task_id} (对话: {conversation_id})")
        return task
    
    def update_task_result(
        self,
        task_id: str,
        result_data: Dict[str, Any],
        is_completed: bool = True,
        error: Optional[str] = None
    ):
        """更新任务执行结果
        
        Args:
            task_id: 任务ID
            result_data: 执行结果数据
            is_completed: 是否已完成
            error: 错误信息（如果有）
        """
        with self.lock:
            task = self.active_tasks.get(task_id)
            if not task:
                logger.warning(f"尝试更新不存在的后台任务: {task_id}")
                return
            
            task.result_data = result_data
            task.is_completed = is_completed
            task.error = error
            
        logger.info(f"更新后台任务结果: {task_id} (完成: {is_completed})")
        
        # 如果任务完成且有回调函数，执行回调
        if is_completed and task.completion_callback:
            try:
                asyncio.create_task(self._execute_completion_callback(task))
            except Exception as e:
                logger.error(f"执行完成回调时出错: {e}")
    
    async def _execute_completion_callback(self, task: BackgroundTask):
        """执行完成回调"""
        try:
            if asyncio.iscoroutinefunction(task.completion_callback):
                await task.completion_callback(task)
            else:
                task.completion_callback(task)
        except Exception as e:
            logger.error(f"执行任务 {task.task_id} 的完成回调时出错: {e}")
    
    def get_task(self, task_id: str) -> Optional[BackgroundTask]:
        """获取任务信息"""
        with self.lock:
            return self.active_tasks.get(task_id)
    
    def is_task_completed(self, task_id: str) -> bool:
        """检查任务是否已完成"""
        task = self.get_task(task_id)
        return task.is_completed if task else False
    
    def remove_task(self, task_id: str):
        """移除任务"""
        with self.lock:
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
                logger.info(f"移除后台任务: {task_id}")
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        with self.lock:
            return len(self.active_tasks)
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        def cleanup_worker():
            while True:
                try:
                    self._cleanup_old_tasks()
                    time.sleep(self._cleanup_interval)
                except Exception as e:
                    logger.error(f"清理后台任务时出错: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.info("后台任务清理线程已启动")
    
    def _cleanup_old_tasks(self):
        """清理过期任务"""
        current_time = time.time()
        tasks_to_remove = []
        
        with self.lock:
            for task_id, task in self.active_tasks.items():
                # 清理超过最大年龄的任务
                if current_time - task.start_time > self._max_task_age:
                    tasks_to_remove.append(task_id)
                # 清理已完成超过1小时的任务
                elif task.is_completed and current_time - task.start_time > 3600:
                    tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            self.remove_task(task_id)
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 个过期的后台任务")


# 全局后台执行管理器实例
background_execution_manager = BackgroundExecutionManager()
