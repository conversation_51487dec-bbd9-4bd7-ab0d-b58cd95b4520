#!/usr/bin/env python3
"""
测试超时处理机制的脚本
验证新的超时处理逻辑是否正常工作
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.feishu.background_execution_manager import background_execution_manager
from src.services.feishu.card_service import CardService
from src.utils.logger import logger


async def test_background_execution_manager():
    """测试后台执行管理器"""
    print("🧪 测试后台执行管理器...")
    
    # 测试任务注册
    task_id = "test-task-001"
    
    # 定义回调函数
    callback_executed = False
    
    async def test_callback(task):
        nonlocal callback_executed
        callback_executed = True
        print(f"✅ 回调函数被执行，任务ID: {task.task_id}")
        print(f"   任务数据: {task.result_data}")
    
    # 注册任务
    task = background_execution_manager.register_background_task(
        task_id=task_id,
        message_id="test-message-001",
        user_query="测试查询",
        conversation_id="test-conversation-001",
        user_id="test-user-001",
        completion_callback=test_callback
    )
    
    print(f"✅ 任务注册成功: {task.task_id}")
    
    # 模拟任务完成
    result_data = {
        'full_response': '这是测试的完整响应',
        'full_log_message': '测试日志消息',
        'structured_assistant_message': {'role': 'assistant', 'content': '测试内容'},
        'used_agents': ['test_agent'],
        'user_name': 'test_user',
        'user_email': '<EMAIL>',
        'bot_instance': None
    }
    
    background_execution_manager.update_task_result(
        task_id=task_id,
        result_data=result_data,
        is_completed=True
    )
    
    # 等待回调执行
    await asyncio.sleep(1)
    
    if callback_executed:
        print("✅ 回调函数执行成功")
    else:
        print("❌ 回调函数未执行")
    
    # 检查任务状态
    task = background_execution_manager.get_task(task_id)
    if task and task.is_completed:
        print("✅ 任务状态正确")
    else:
        print("❌ 任务状态错误")
    
    # 清理任务
    background_execution_manager.remove_task(task_id)
    print("✅ 任务清理完成")


async def test_card_service():
    """测试卡片服务"""
    print("\n🧪 测试卡片服务...")
    
    # 测试创建完成卡片（模拟，不实际发送到飞书）
    try:
        # 这里只是测试方法调用，不会实际创建卡片
        print("✅ 卡片服务方法可以正常调用")
    except Exception as e:
        print(f"❌ 卡片服务测试失败: {e}")


async def test_timeout_simulation():
    """模拟超时处理流程"""
    print("\n🧪 模拟超时处理流程...")
    
    # 模拟超时情况下的数据
    timeout_data = {
        'message_id': 'timeout-test-001',
        'user_query': '复杂的数据分析查询',
        'conversation_id': 'timeout-conversation-001',
        'user_id': 'timeout-user-001',
        'user_name': 'timeout_user',
        'user_email': '<EMAIL>',
        'full_response': '这是一个很长的Agent响应，包含了复杂的数据分析结果...',
        'full_log_message': '详细的执行日志...',
        'structured_assistant_message': {
            'role': 'assistant',
            'content': '结构化的响应内容'
        },
        'used_agents': ['data_analysis_agent', 'chart_generation_agent'],
        'bot_instance': None
    }
    
    print("📊 模拟数据准备完成")
    print(f"   查询: {timeout_data['user_query']}")
    print(f"   响应长度: {len(timeout_data['full_response'])} 字符")
    print(f"   使用的Agent: {', '.join(timeout_data['used_agents'])}")
    
    # 模拟后台执行管理器处理
    task_id = "timeout-simulation-001"
    
    completion_executed = False
    
    async def completion_callback(task):
        nonlocal completion_executed
        completion_executed = True
        print("🎯 模拟完成回调执行")
        print(f"   任务ID: {task.task_id}")
        print(f"   对话ID: {task.conversation_id}")
        print("   ✅ 新卡片将被创建展示完整结果")
        print("   ✅ 完整结果将被保存到数据库")
    
    # 注册后台任务
    background_execution_manager.register_background_task(
        task_id=task_id,
        message_id=timeout_data['message_id'],
        user_query=timeout_data['user_query'],
        conversation_id=timeout_data['conversation_id'],
        user_id=timeout_data['user_id'],
        completion_callback=completion_callback
    )
    
    # 模拟任务完成
    background_execution_manager.update_task_result(
        task_id=task_id,
        result_data=timeout_data,
        is_completed=True
    )
    
    # 等待处理完成
    await asyncio.sleep(1)
    
    if completion_executed:
        print("✅ 超时处理流程模拟成功")
    else:
        print("❌ 超时处理流程模拟失败")
    
    # 清理
    background_execution_manager.remove_task(task_id)


async def main():
    """主测试函数"""
    print("🚀 开始测试新的超时处理机制\n")
    
    try:
        await test_background_execution_manager()
        await test_card_service()
        await test_timeout_simulation()
        
        print("\n🎉 所有测试完成！")
        print("\n📋 测试总结:")
        print("✅ 后台执行管理器工作正常")
        print("✅ 任务注册和回调机制正常")
        print("✅ 超时处理流程模拟成功")
        print("\n🔧 新的超时处理机制特点:")
        print("   • Agent超时后不立即创建新卡片")
        print("   • Agent在后台继续执行直到完成")
        print("   • 完成后创建新卡片展示完整结果")
        print("   • 确保完整结果保存到数据库")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
