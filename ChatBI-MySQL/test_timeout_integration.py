#!/usr/bin/env python3
"""
集成测试：模拟真实的超时处理场景
测试完整的超时处理流程，包括StreamProcessor和QueryProcessor的协作
"""
import asyncio
import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.feishu.stream_processor import StreamProcessor, StreamState
from src.services.feishu.query_processor import QueryProcessor
from src.services.feishu.background_execution_manager import background_execution_manager
from src.utils.logger import logger


class MockAgent:
    """模拟Agent"""
    def __init__(self, response_time=5):
        self.response_time = response_time
    
    def create_agent(self):
        return self


class MockResult:
    """模拟Agent执行结果"""
    def __init__(self, events, response_time=5):
        self.events = events
        self.response_time = response_time
    
    async def stream_events(self):
        """模拟流事件"""
        for i, event in enumerate(self.events):
            # 模拟处理时间
            await asyncio.sleep(self.response_time / len(self.events))
            yield event
    
    def to_input_list(self):
        return [{"role": "assistant", "content": "模拟的结构化响应"}]


class MockEvent:
    """模拟流事件"""
    def __init__(self, event_type, content=""):
        self.type = event_type
        self.content = content


async def test_stream_processor_timeout():
    """测试StreamProcessor的超时处理"""
    print("🧪 测试StreamProcessor超时处理...")
    
    # 创建模拟的长时间运行的Agent
    events = [
        MockEvent("raw_response_event", "开始分析数据..."),
        MockEvent("raw_response_event", "正在查询数据库..."),
        MockEvent("raw_response_event", "生成图表中..."),
        MockEvent("raw_response_event", "分析完成！"),
    ]
    
    # 模拟一个需要15分钟的Agent（超过10分钟超时阈值）
    mock_result = MockResult(events, response_time=15 * 60)  # 15分钟
    
    # 创建StreamState
    state = StreamState(initial_sequence=1)
    
    # 模拟StreamProcessor处理
    processor = StreamProcessor()
    
    # 设置较短的超时时间用于测试（5秒）
    original_timeout = processor.timeout_minutes
    processor.timeout_minutes = 5 / 60  # 5秒转换为分钟
    
    start_time = time.time()
    
    try:
        # 模拟处理流事件
        await processor._process_stream_events(mock_result, state, "test-card-001", start_time)
        
        # 检查是否正确检测到超时
        if state.timeouted:
            print("✅ StreamProcessor正确检测到超时")
            print(f"   处理了 {state.event_count} 个事件")
            print(f"   收集的响应长度: {len(state.full_response)} 字符")
        else:
            print("❌ StreamProcessor未检测到超时")
    
    except Exception as e:
        print(f"⚠️  StreamProcessor处理过程中出现异常: {e}")
    
    finally:
        # 恢复原始超时时间
        processor.timeout_minutes = original_timeout
    
    return state.timeouted


async def test_query_processor_timeout_handling():
    """测试QueryProcessor的超时处理"""
    print("\n🧪 测试QueryProcessor超时处理...")
    
    # 模拟超时情况下的数据
    test_data = {
        'message_id': 'integration-test-001',
        'user_query': '请分析过去一年的销售数据并生成趋势图',
        'conversation_id': 'integration-conversation-001',
        'user_id': 'integration-user-001',
        'user_name': 'integration_user',
        'user_email': '<EMAIL>',
        'card_id': 'integration-card-001',
        'full_response': '''# 销售数据分析报告

## 数据概览
- 总销售额: ¥12,345,678
- 订单数量: 8,765
- 平均客单价: ¥1,408

## 趋势分析
1. Q1销售额增长15%
2. Q2销售额增长8%
3. Q3销售额增长22%
4. Q4销售额增长5%

## 建议
基于数据分析，建议在Q3加大营销投入...''',
        'full_log_message': '''[INFO] 开始数据分析
[INFO] 连接数据库成功
[INFO] 查询销售数据
[INFO] 数据清洗完成
[INFO] 生成趋势图
[INFO] 分析完成''',
        'structured_assistant_message': {
            'role': 'assistant',
            'content': '销售数据分析已完成，请查看详细报告。'
        },
        'used_agents': ['data_analysis_agent', 'chart_generation_agent'],
        'bot_instance': None,
        'element_id': 'integration-element-001'
    }
    
    # 测试超时处理
    try:
        task_id = await QueryProcessor._handle_timeout_with_background_execution(
            message_id=test_data['message_id'],
            user_query=test_data['user_query'],
            conversation_id=test_data['conversation_id'],
            user_id=test_data['user_id'],
            user_name=test_data['user_name'],
            user_email=test_data['user_email'],
            card_id=test_data['card_id'],
            full_response=test_data['full_response'],
            full_log_message=test_data['full_log_message'],
            structured_assistant_message=test_data['structured_assistant_message'],
            used_agents=test_data['used_agents'],
            bot_instance=test_data['bot_instance'],
            element_id=test_data['element_id']
        )
        
        print(f"✅ QueryProcessor超时处理成功，任务ID: {task_id}")
        
        # 等待后台处理完成
        await asyncio.sleep(2)
        
        # 检查任务状态
        task = background_execution_manager.get_task(task_id)
        if task and task.is_completed:
            print("✅ 后台任务已完成")
            print(f"   任务数据包含 {len(task.result_data)} 个字段")
        else:
            print("❌ 后台任务未完成")
        
        # 清理任务
        background_execution_manager.remove_task(task_id)
        return True
        
    except Exception as e:
        print(f"❌ QueryProcessor超时处理失败: {e}")
        return False


async def test_end_to_end_timeout_scenario():
    """端到端超时场景测试"""
    print("\n🧪 端到端超时场景测试...")
    
    print("📋 模拟场景:")
    print("   1. 用户发起复杂查询")
    print("   2. Agent开始执行，预计需要15分钟")
    print("   3. 10分钟后检测到超时")
    print("   4. 停止流式更新，Agent继续后台执行")
    print("   5. Agent完成后创建新卡片展示结果")
    print("   6. 保存完整结果到数据库")
    
    # 模拟用户查询
    user_query = "请分析公司过去三年的财务数据，包括收入、支出、利润趋势，并预测未来一年的财务状况"
    
    print(f"\n📊 用户查询: {user_query}")
    print("⏰ 开始执行...")
    
    # 模拟Agent执行过程
    execution_steps = [
        "🔍 正在连接财务数据库...",
        "📊 正在提取三年财务数据...",
        "🧮 正在进行数据清洗和预处理...",
        "📈 正在分析收入趋势...",
        "📉 正在分析支出模式...",
        "💰 正在计算利润指标...",
        "🔮 正在构建预测模型...",
        "📋 正在生成分析报告...",
        "✅ 分析完成！"
    ]
    
    for i, step in enumerate(execution_steps):
        print(f"   {step}")
        await asyncio.sleep(0.5)  # 模拟处理时间
        
        # 在第5步模拟超时
        if i == 4:
            print("\n⏰ 检测到超时（10分钟），切换到后台执行模式")
            print("   📱 用户界面停止更新")
            print("   🔄 Agent继续在后台执行...")
    
    print("\n🎯 Agent在后台完成执行")
    print("📱 创建新的飞书消息卡片")
    print("💾 保存完整结果到数据库")
    print("✅ 用户收到完整的分析结果")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始集成测试：超时处理机制\n")
    
    try:
        # 测试StreamProcessor超时检测
        timeout_detected = await test_stream_processor_timeout()
        
        # 测试QueryProcessor超时处理
        query_processor_success = await test_query_processor_timeout_handling()
        
        # 端到端场景测试
        end_to_end_success = await test_end_to_end_timeout_scenario()
        
        print("\n" + "="*60)
        print("📊 集成测试结果总结")
        print("="*60)
        print(f"✅ StreamProcessor超时检测: {'通过' if timeout_detected else '失败'}")
        print(f"✅ QueryProcessor超时处理: {'通过' if query_processor_success else '失败'}")
        print(f"✅ 端到端场景测试: {'通过' if end_to_end_success else '失败'}")
        
        if timeout_detected and query_processor_success and end_to_end_success:
            print("\n🎉 所有集成测试通过！")
            print("\n🔧 新超时处理机制已就绪:")
            print("   • 流式响应超时检测正常")
            print("   • 后台执行管理器工作正常")
            print("   • 完成回调机制正常")
            print("   • 数据库保存逻辑正常")
        else:
            print("\n⚠️  部分测试未通过，需要进一步检查")
        
    except Exception as e:
        print(f"\n❌ 集成测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
